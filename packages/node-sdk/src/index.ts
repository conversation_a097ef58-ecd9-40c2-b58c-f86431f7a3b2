/**
 * @file Main entry point for the ezWorkflow node SDK
 */

// Export types
export * from './types';

// Export field builders
export * from './field-builders';

// Export auth builders
export * from './auth-builders';

// Export core functions
export { defineNode } from './define-node';
export { defineExecutor } from './define-executor';
export { defineTrigger } from './define-trigger';


// Export specialized UI component functions
export { defineDashboardWidget } from './define-dashboard-widget';
export { defineModal } from './define-modal';
export { defineDrawer } from './define-drawer';
export { definePage } from './define-page';
export { defineFunnelBuilderBlock } from './define-funnel-builder-block';
export { defineEmailBuilderBlock } from './define-email-builder-block';


// Export registry
export { componentRegistry, nodeRegistry } from './registry';

// Export server
import { NodeServer, ServerOptions } from './server';
export { NodeServer, ServerOptions };

// Export configuration utilities
export { getConfig, hasConfig, getConfigPath } from '@ezworkflow/project-config';
export type { EzwConfig, AuthorInfo, HonoRuntimeTemplate } from '@ezworkflow/project-config';
export { ConfigValidationError, ConfigNotFoundError } from '@ezworkflow/project-config';

/**
 * Create a server middleware for ezWorkflow
 *
 * This function creates a Hono app instance that can be used with a parent Hono app
 * to expose ezWorkflow nodes, executors, triggers, and UI components through an API.
 *
 * The server will use components from the registry automatically.
 *
 * You can optionally provide an API key for authentication and permissions to restrict access.
 *
 * @example
 * ```typescript
 * import { Hono } from 'hono';
 * import { getServer } from '@ezworkflow/node-sdk';
 *
 * const app = new Hono();
 *
 * // Register ezWorkflow routes with authentication and permissions
 * app.route('/api/ezw', getServer({
 *   apiKey: 'your-secret-api-key',
 *   permissions: ['read', 'execute'],
 * }));
 *
 * export default app;
 * ```
 *
 * @param options Server options including apiKey and permissions
 * @returns A Hono app instance that can be used with app.route()
 */
export function getServer(options: ServerOptions = {}) {
  const server = new NodeServer(options);
  return server.getApp();
}
