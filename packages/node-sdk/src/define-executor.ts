/**
 * @file Function for defining node executors
 */

import { ExecutionContext, ExecutionResult, NodeExecutor, Logger, Node } from './types';
import { componentRegistry } from './registry';

// We don't need these types anymore as we're using conditional types directly in the function signature

/**
 * Type to extract input types from a node
 */
type NodeInputs<N> = N extends Node
  ? Record<keyof N['definition']['inputs'], unknown>
  : N extends string
    ? (ReturnType<typeof componentRegistry.getNode> extends Node
      ? Record<keyof ReturnType<typeof componentRegistry.getNode>['definition']['inputs'], unknown>
      : Record<string, unknown>)
    : Record<string, unknown>;

/**
 * Type to extract config types from a node
 */
type NodeConfig<N> = N extends Node
  ? N['definition']['config'] extends Record<string, unknown>
    ? Record<keyof N['definition']['config'], unknown>
    : never
  : N extends string
    ? (ReturnType<typeof componentRegistry.getNode> extends Node
      ? ReturnType<typeof componentRegistry.getNode>['definition']['config'] extends Record<string, unknown>
        ? Record<keyof ReturnType<typeof componentRegistry.getNode>['definition']['config'], unknown>
        : never
      : never)
    : never;

/**
 * Type to extract auth types from a node
 */
type NodeAuth<N> = N extends Node
  ? N['definition']['auth'] extends Record<string, unknown>
    ? Record<string, unknown>
    : never
  : N extends string
    ? (ReturnType<typeof componentRegistry.getNode> extends Node
      ? ReturnType<typeof componentRegistry.getNode>['definition']['auth'] extends Record<string, unknown>
        ? Record<string, unknown>
        : never
      : never)
    : never;

/**
 * Type to extract output types from a node
 */
type NodeOutputs<N> = N extends Node
  ? Record<keyof N['definition']['outputs'], unknown>
  : N extends string
    ? (ReturnType<typeof componentRegistry.getNode> extends Node
      ? Record<keyof ReturnType<typeof componentRegistry.getNode>['definition']['outputs'], unknown>
      : Record<string, unknown>)
    : Record<string, unknown>;

/**
 * Define a node executor
 *
 * @param nodeId ID of the node this executor is for
 * @param executor Function that executes the node logic
 * @returns Node executor function
 */
// Define different executor function types based on what's in the node
type ExecutorFnWithInputOnly<I, O> = (context: {
  inputs: I;
  logger: Logger;
}) => Promise<{
  success: boolean;
  outputs: O;
  error?: string;
  logs?: string[];
}>;

type ExecutorFnWithInputAndConfig<I, C, O> = (context: {
  inputs: I;
  config: C;
  logger: Logger;
}) => Promise<{
  success: boolean;
  outputs: O;
  error?: string;
  logs?: string[];
}>;

type ExecutorFnWithInputAndAuth<I, A, O> = (context: {
  inputs: I;
  logger: Logger;
  auth: A;
}) => Promise<{
  success: boolean;
  outputs: O;
  error?: string;
  logs?: string[];
}>;

type ExecutorFnWithAll<I, C, A, O> = (context: {
  inputs: I;
  config: C;
  logger: Logger;
  auth: A;
}) => Promise<{
  success: boolean;
  outputs: O;
  error?: string;
  logs?: string[];
}>;

// Helper type to select the right executor function type
type SelectExecutorFn<I, C, A, O> =
  C extends never
    ? (A extends never
      ? ExecutorFnWithInputOnly<I, O>
      : ExecutorFnWithInputAndAuth<I, A, O>)
    : (A extends never
      ? ExecutorFnWithInputAndConfig<I, C, O>
      : ExecutorFnWithAll<I, C, A, O>);

export const defineExecutor = <
  // Accept either a node object or a node ID string
  N extends Node | string,
  // Infer input types from the node if provided, otherwise use a generic record
  I = NodeInputs<N>,
  // Infer config types from the node if provided, otherwise use never
  C = NodeConfig<N>,
  // Infer auth types from the node if provided, otherwise use never
  A = NodeAuth<N>,
  // Infer output types from the node if provided, otherwise use a generic record
  O = NodeOutputs<N>
>(
  // The node ID or node object
  nodeId: N,
  // The executor function with properly typed parameters based on what's in the node
  executor: SelectExecutorFn<I, C, A, O>
): NodeExecutor<O> => {
  // Get the node ID
  const id = typeof nodeId === 'string' ? nodeId : nodeId.definition.id;

  // Log a warning if the node is not registered
  if (typeof nodeId === 'string' && !componentRegistry.hasNode(id)) {
    console.warn(`Node with ID ${id} is not registered. Type inference may not work correctly.`);
  }

  // Return a wrapped executor function
  return async (context: ExecutionContext): Promise<ExecutionResult<O>> => {
    try {
      // Create the executor context based on what's available in the node
      const executorContext: any = {
        inputs: context.inputs as I,
        logger: context.logger,
      };

      // Add config and auth if they're available
      if (context.config) {
        executorContext.config = context.config as C;
      }

      if (context.auth) {
        executorContext.auth = context.auth as A;
      }

      // Execute the user-provided executor
      const result = await executor(executorContext);

      // Return the result
      return {
        success: result.success,
        outputs: result.outputs,
        error: result.error,
        logs: result.logs,
      };
    } catch (error) {
      // Handle any uncaught errors
      return {
        success: false,
        outputs: {} as O,
        error: error instanceof Error ? error.message : String(error),
        logs: [],
      };
    }
  };
};
