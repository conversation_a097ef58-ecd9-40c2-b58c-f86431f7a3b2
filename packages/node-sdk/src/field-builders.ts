/**
 * @file Field builders for defining node inputs, outputs, and configuration
 */

import {
  DataType,
  FieldType,
  FieldDefinition,
  FieldBuilder,
  StringFieldBuilder,
  NumberFieldBuilder,
  BooleanFieldBuilder,
  OptionsFieldBuilder,
  ObjectFieldBuilder,
  ArrayFieldBuilder,
  StreamFieldBuilder,
  FileFieldBuilder,
} from './types';

/**
 * Base field builder implementation
 */
class BaseFieldBuilder<T> implements FieldBuilder<T> {
  protected definition: FieldDefinition;

  constructor(type: DataType | FieldType) {
    this.definition = {
      type,
      required: false,
    };
  }

  description(desc: string): this {
    this.definition.description = desc;
    return this;
  }

  default(value: T): this {
    this.definition.default = value;
    return this;
  }

  required(): this {
    this.definition.required = true;
    return this;
  }

  optional(): this {
    this.definition.required = false;
    return this;
  }

  showIf(condition: (parent: Record<string, unknown>, inputs?: Record<string, unknown>) => boolean): this {
    this.definition.showIf = condition;
    return this;
  }

  group(name: string): this {
    this.definition.group = name;
    return this;
  }

  collapsible(value = true): this {
    this.definition.collapsible = value;
    return this;
  }

  metadata(data: Record<string, unknown>): this {
    this.definition.metadata = { ...(this.definition.metadata || {}), ...data };
    return this;
  }

  validate(validator: (value: T) => void | string | boolean): this {
    this.metadata({ validator });
    return this;
  }

  transform(transformer: (value: T, inputs?: Record<string, unknown>) => unknown): this {
    this.metadata({ transformer });
    return this;
  }

  getDefinition(): FieldDefinition {
    return { ...this.definition };
  }
}

/**
 * String field builder implementation
 */
class StringFieldBuilderImpl extends BaseFieldBuilder<string> implements StringFieldBuilder {
  constructor() {
    super(DataType.STRING);
  }

  minLength(length: number): this {
    this.metadata({ minLength: length });
    return this;
  }

  maxLength(length: number): this {
    this.metadata({ maxLength: length });
    return this;
  }

  pattern(regex: RegExp): this {
    this.metadata({ pattern: regex.source });
    return this;
  }

  format(format: string): this {
    this.metadata({ format });
    return this;
  }

  multiline(value = true): this {
    this.metadata({ multiline: value });
    return this;
  }

  secret(value = true): this {
    this.metadata({ secret: value });
    return this;
  }

  fromEnv(envName: string): this {
    this.metadata({ fromEnv: envName });
    return this;
  }
}

/**
 * Number field builder implementation
 */
class NumberFieldBuilderImpl extends BaseFieldBuilder<number> implements NumberFieldBuilder {
  constructor() {
    super(DataType.NUMBER);
  }

  min(value: number): this {
    this.metadata({ min: value });
    return this;
  }

  max(value: number): this {
    this.metadata({ max: value });
    return this;
  }

  step(value: number): this {
    this.metadata({ step: value });
    return this;
  }

  integer(): this {
    this.metadata({ integer: true });
    return this;
  }

  fromEnv(envName: string): this {
    this.metadata({ fromEnv: envName });
    return this;
  }
}

/**
 * Boolean field builder implementation
 */
class BooleanFieldBuilderImpl extends BaseFieldBuilder<boolean> implements BooleanFieldBuilder {
  constructor() {
    super(DataType.BOOLEAN);
  }
}

/**
 * Options field builder implementation
 */
class OptionsFieldBuilderImpl extends BaseFieldBuilder<string | string[]> implements OptionsFieldBuilder {
  constructor(options?: Array<{ label: string; value: string; icon?: string }>) {
    super(FieldType.SELECT);
    if (options) {
      this.options(options);
    }
  }

  options(options: Array<{ label: string; value: string; icon?: string }>): this {
    this.metadata({ options });
    return this;
  }

  multiple(value = true): this {
    if (value) {
      this.definition.type = FieldType.MULTISELECT;
    } else {
      this.definition.type = FieldType.SELECT;
    }
    return this;
  }

  fromEnv(envName: string): this {
    this.metadata({ fromEnv: envName });
    return this;
  }
}

/**
 * Object field builder implementation
 */
class ObjectFieldBuilderImpl extends BaseFieldBuilder<Record<string, unknown>> implements ObjectFieldBuilder {
  constructor(properties?: Record<string, FieldDefinition>) {
    super(DataType.OBJECT);
    if (properties) {
      this.properties(properties);
    }
  }

  properties(props: Record<string, FieldDefinition>): this {
    this.metadata({ properties: props });
    return this;
  }
}

/**
 * Array field builder implementation
 */
class ArrayFieldBuilderImpl extends BaseFieldBuilder<unknown[]> implements ArrayFieldBuilder {
  constructor(itemType?: FieldDefinition) {
    super(DataType.ARRAY);
    if (itemType) {
      this.items(itemType);
    }
  }

  items(itemType: FieldDefinition): this {
    this.metadata({ items: itemType });
    return this;
  }

  minItems(count: number): this {
    this.metadata({ minItems: count });
    return this;
  }

  maxItems(count: number): this {
    this.metadata({ maxItems: count });
    return this;
  }
}

/**
 * Stream field builder implementation
 */
class StreamFieldBuilderImpl extends BaseFieldBuilder<unknown> implements StreamFieldBuilder {
  constructor() {
    super(DataType.STREAM);
  }

  format(format: string | ((inputs: Record<string, unknown>) => string)): this {
    this.metadata({ format });
    return this;
  }

  chunkSize(size: number): this {
    this.metadata({ chunkSize: size });
    return this;
  }
}

/**
 * Create a string field builder
 */
export const string = (): StringFieldBuilder => new StringFieldBuilderImpl();

/**
 * Create a number field builder
 */
export const number = (): NumberFieldBuilder => new NumberFieldBuilderImpl();

/**
 * Create a boolean field builder
 */
export const boolean = (): BooleanFieldBuilder => new BooleanFieldBuilderImpl();

/**
 * Create an options field builder
 */
export const options = (options?: Array<{ label: string; value: string; icon?: string }>): OptionsFieldBuilder =>
  new OptionsFieldBuilderImpl(options);

/**
 * Create an object field builder
 */
export const object = (properties?: Record<string, FieldDefinition>): ObjectFieldBuilder =>
  new ObjectFieldBuilderImpl(properties);

/**
 * Create an array field builder
 */
export const array = (itemType?: FieldDefinition): ArrayFieldBuilder =>
  new ArrayFieldBuilderImpl(itemType);

/**
 * Create a stream field builder
 */
export const stream = (): StreamFieldBuilder => new StreamFieldBuilderImpl();

/**
 * File field builder implementation
 */
class FileFieldBuilderImpl extends BaseFieldBuilder<unknown> implements FileFieldBuilder {
  constructor() {
    super(DataType.FILE);
  }

  accept(mimeTypes: string[]): this {
    this.metadata({ accept: mimeTypes });
    return this;
  }

  maxSize(sizeInBytes: number): this {
    this.metadata({ maxSize: sizeInBytes });
    return this;
  }

  multiple(value = true): this {
    this.metadata({ multiple: value });
    return this;
  }
}

/**
 * Create a file field builder
 */
export const file = (): FileFieldBuilder => new FileFieldBuilderImpl();
