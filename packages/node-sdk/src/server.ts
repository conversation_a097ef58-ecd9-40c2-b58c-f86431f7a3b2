/**
 * @file Server implementation for ezWorkflow
 *
 * This file provides the server implementation for ezWorkflow, which allows
 * users to expose their nodes, executors, and triggers through a Hono.js API.
 */

import { Hono } from 'hono';
import { Node, NodeExecutor, TriggerDefinition } from './types';
import { componentRegistry } from './registry';
import { PageDefinition } from './define-page';
import { DashboardWidgetDefinition } from './define-dashboard-widget';
import { ModalDefinition } from './define-modal';
import { DrawerDefinition } from './define-drawer';
import { EmailBuilderBlockDefinition } from './define-email-builder-block';
import { FunnelBuilderBlockDefinition } from './define-funnel-builder-block';
import { Logger } from './utils/logger';

/**
 * Options for the ezWorkflow server
 */
export interface ServerOptions {
  /**
   * API key for authentication
   * If provided, all requests must include this key in the Authorization header
   */
  apiKey?: string;

  /**
   * Permissions array defining what operations are allowed
   * Available permissions: 'read', 'execute', 'admin'
   * - 'read': Allow reading node, executor, trigger, and UI component information
   * - 'execute': Allow executing nodes
   * - 'admin': Allow all operations
   * If not provided, all operations are allowed
   */
  permissions?: string[];

  /**
   * App type
   * 'partner' or 'account'
   */
  appType?: 'partner' | 'account';

  /**
   * App version
   * The version of the app
   */
  version?: string;

  /**
   * App author
   * The author of the app
   */
  author?: string | {
    name: string; // The name of the author
    email?: string; // The email of the author
    url?: string; // The URL of the author
    support?: string; // The support URL of the author
    logo?: string; // The logo of the author
  };

  /**
   * App categories
   * The categories of the app
   */
  categories?: string[];

  /**
   * App tags
   * The tags of the app
   */
  tags?: string[];
}

/**
 * Server class for ezWorkflow
 *
 * This class provides a server implementation for ezWorkflow, which allows
 * users to expose their nodes, executors, and triggers through a Hono.js API.
 */
export class NodeServer {
  private app: Hono;
  private nodes: Map<string, Node> = new Map();
  private executors: Map<string, NodeExecutor> = new Map();
  private triggers: Map<string, TriggerDefinition> = new Map();
  private pages: Map<string, PageDefinition> = new Map();
  private modals: Map<string, ModalDefinition> = new Map();
  private dashboardWidgets: Map<string, DashboardWidgetDefinition> = new Map();
  private drawers: Map<string, DrawerDefinition> = new Map();
  private emailBuilderBlocks: Map<string, EmailBuilderBlockDefinition> = new Map();
  private funnelsBuilderBlocks: Map<string, FunnelBuilderBlockDefinition> = new Map();
  private apiKey?: string;
  private permissions: string[] = [];
  private options: ServerOptions = {};

  /**
   * Create a new NodeServer instance
   *
   * @param options Server options
   */
  constructor(options: ServerOptions = {}) {
    this.app = new Hono();
    this.options = options;

    // Store API key and permissions
    this.apiKey = options.apiKey;
    this.permissions = options.permissions || [];

    // If API key is provided, set up authentication middleware
    if (this.apiKey) {
      this.setupAuthMiddleware();
    }

    // Use nodes from the registry
    for (const node of componentRegistry.getAllNodes()) {
      this.nodes.set(node.definition.id, node);
    }

    // Use executors from the registry
    const executors = componentRegistry.getAllExecutors();
    for (const executor of executors) {
      // We need to find the node ID for this executor
      // For now, we'll assume the executor ID is the same as the node ID
      for (const node of this.nodes.values()) {
        if (node.executor === executor) {
          this.executors.set(node.definition.id, executor);
          break;
        }
      }
    }

    // Use triggers from the registry
    for (const trigger of componentRegistry.getAllTriggers()) {
      this.triggers.set(trigger.id, trigger);
    }

    // Set up routes
    this.setupRoutes();
  }

  /**
   * Set up authentication middleware
   *
   * This middleware checks for the API key in the Authorization header
   * and verifies that it matches the one provided to the server.
   * The health endpoint is excluded from authentication.
   */
  private setupAuthMiddleware(): void {
    this.app.use('*', async (c, next) => {
      // Skip authentication for health endpoint
      if (c.req.path === '/health') {
        await next();
        return;
      }

      const authHeader = c.req.header('Authorization');

      // Check if Authorization header is present and has the correct format
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return c.json({ error: 'Missing or invalid Authorization header' }, 401);
      }

      // Extract the API key from the Authorization header
      const providedApiKey = authHeader.substring(7); // Remove 'Bearer ' prefix

      // Verify the API key
      if (providedApiKey !== this.apiKey) {
        return c.json({ error: 'Invalid API key' }, 401);
      }

      // API key is valid, continue to the next middleware or route handler
      await next();
    });
  }

  /**
   * Check if the current request has the required permission
   *
   * @param permission The permission to check
   * @returns True if the permission is granted, false otherwise
   */
  private hasPermission(permission: string): boolean {
    // If no permissions are specified, all operations are allowed
    if (this.permissions.length === 0) {
      return true;
    }

    // 'admin' permission grants access to all operations
    if (this.permissions.includes('admin')) {
      return true;
    }

    // Check if the specific permission is granted
    return this.permissions.includes(permission);
  }

  /**
   * Set up the routes for the server
   */
  private setupRoutes(): void {
    // Root endpoint
    this.app.get('/', (c) => {
      return c.json({
        status: 'ok',
        version: this.options.version || '1.0.0'
      });
    });

    // Health check endpoint
    this.app.get('/health', (c) => {
      return c.json({ status: 'ok' });
    });

    // Get nodes
    this.app.get('/nodes', (c) => {
      try {

        const nodesObj: Record<string, {
          name: string;
          description: string;
          categories: string[];
          tags: string[];
        }> = {};

        for (const [id, node] of this.nodes.entries()) {
          const { name, description, category, tags } = node.definition;

          // Ensure category is always an array
          const categories = Array.isArray(category) ? category : [category];

          nodesObj[id] = {
            name,
            description,
            categories,
            tags: tags || [],
          };
        }
        return c.json({ nodes: nodesObj });
      } catch (error) {
        console.error('Error getting nodes:', error);
        return c.json({ error: 'Failed to get nodes' }, 500);
      }
    });

    // Get node by ID
    this.app.get('/nodes/:id', (c) => {
      try {
        const id = c.req.param('id');
        const node = this.nodes.get(id);

        if (!node) {
          return c.json({ error: `Node with ID ${id} not found` }, 404);
        }

        return c.json({ node: node.definition });
      } catch (error) {
        console.error('Error getting node:', error);
        return c.json({ error: 'Failed to get node' }, 500);
      }
    });

    // Execute node
    this.app.post('/nodes/:id/execute', async (c) => {
      try {
        const id = c.req.param('id');
        const node = this.nodes.get(id);

        if (!node) {
          return c.json({ error: `Node with ID ${id} not found` }, 404);
        }

        const executor = this.executors.get(id);

        if (!executor) {
          return c.json({ error: `Executor for node with ID ${id} not found` }, 404);
        }

        const body = await c.req.json();
        const { inputs, config, auth } = body;

        // Create a simple logger
        const logger = {
          info: (message: string) => console.info(`[${id}] ${message}`),
          warn: (message: string) => console.warn(`[${id}] ${message}`),
          error: (message: string) => console.error(`[${id}] ${message}`),
          debug: (message: string) => console.debug(`[${id}] ${message}`),
        };

        // Execute the node
        const result = await executor({ inputs, config, auth, logger });

        // Add additional fields for compatibility
        return c.json({
          ...result,
          delay: 0,
          reExecute: false,
          tryAgain: false,
          retryDelay: 0,
        });
      } catch (error) {
        console.error('Error executing node:', error);
        return c.json({
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
          outputs: {},
          delay: 0,
          reExecute: false,
          tryAgain: false,
          retryDelay: 0,
        }, 500);
      }
    });

    // Get triggers
    this.app.get('/triggers', (c) => {
      try {
        // Check if the client has read permission
        if (!this.hasPermission('read') && !this.hasPermission('admin')) {
          return c.json({ error: 'Permission denied' }, 403);
        }

        const triggersObj: Record<string, any> = {};

        for (const [id, trigger] of this.triggers.entries()) {
          const { name, version, categories, tags, type } = trigger;

          triggersObj[id] = {
            name,
            version,
            categories,
            tags,
            type,
          };
        }

        return c.json({ triggers: triggersObj });
      } catch (error) {
        console.error('Error getting triggers:', error);
        return c.json({ error: 'Failed to get triggers' }, 500);
      }
    });

    // Get trigger by ID
    this.app.get('/triggers/:id', (c) => {
      try {
        // Check if the client has read permission
        if (!this.hasPermission('read') && !this.hasPermission('admin')) {
          return c.json({ error: 'Permission denied' }, 403);
        }

        const id = c.req.param('id');
        const trigger = this.triggers.get(id);

        if (!trigger) {
          return c.json({ error: `Trigger with ID ${id} not found` }, 404);
        }

        return c.json({ trigger });
      } catch (error) {
        console.error('Error getting trigger:', error);
        return c.json({ error: 'Failed to get trigger' }, 500);
      }
    });

    this.app.get('/pages', (c) => {
      try {
        // Check if the client has read permission
        if (!this.hasPermission('read') && !this.hasPermission('admin')) {
          return c.json({ error: 'Permission denied' }, 403);
        }

        const pagesObj: Record<string, Partial<Omit<PageDefinition, 'id'>>> = {};

        for (const [id, page] of this.pages.entries()) {
          const { name, version, categories, tags, visibility } = page;

          pagesObj[id] = {
            name,
            version,
            categories,
            tags,
            visibility,
          };
        }

        return c.json({ pages: pagesObj });
      }
      catch (error) {
        console.error('Error getting pages:', error);
        return c.json({ error: 'Failed to get pages' }, 500);
      }
    });

  }

  /**
   * Get the Hono app instance
   *
   * @returns The Hono app instance
   */
  getApp(): Hono {
    return this.app;
  }

  /**
   * Get the middleware function for the server
   *
   * @returns The middleware function
   */
  getMiddleware() {
    return this.app.fetch;
  }
}
