/**
 * @file Server implementation for ezWorkflow
 *
 * This file provides the server implementation for ezWorkflow, which allows
 * users to expose their nodes, executors, and triggers through a Hono.js API.
 */

import { Hono } from "hono";
import { Node, NodeExecutor, TriggerDefinition } from "./types";
import { componentRegistry } from "./registry";
import { PageDefinition } from "./define-page";
import { DashboardWidgetDefinition } from "./define-dashboard-widget";
import { ModalDefinition } from "./define-modal";
import { DrawerDefinition } from "./define-drawer";
import { EmailBuilderBlockDefinition } from "./define-email-builder-block";
import { FunnelBuilderBlockDefinition } from "./define-funnel-builder-block";

import { createRouter } from "./server/routes/index";
import { ServerContext } from "./server/routes/middleware";

/**
 * Options for the ezWorkflow server
 */
export interface ServerOptions {
  /**
   * API key for authentication
   * If provided, all requests must include this key in the Authorization header
   */
  apiKey?: string;

  /**
   * Permissions array defining what operations are allowed
   * Available permissions: 'read', 'execute', 'admin'
   * - 'read': Allow reading node, executor, trigger, and UI component information
   * - 'execute': Allow executing nodes
   * - 'admin': Allow all operations
   * If not provided, all operations are allowed
   */
  permissions?: string[];

  /**
   * App type
   * 'partner' or 'account'
   */
  appType?: "partner" | "account";

  /**
   * App version
   * The version of the app
   */
  version?: string;

  /**
   * App author
   * The author of the app
   */
  author?:
    | string
    | {
        name: string; // The name of the author
        email?: string; // The email of the author
        url?: string; // The URL of the author
        support?: string; // The support URL of the author
        logo?: string; // The logo of the author
      };

  /**
   * App categories
   * The categories of the app
   */
  categories?: string[];

  /**
   * App tags
   * The tags of the app
   */
  tags?: string[];
}

/**
 * Server class for ezWorkflow
 *
 * This class provides a server implementation for ezWorkflow, which allows
 * users to expose their nodes, executors, and triggers through a Hono.js API.
 */
export class NodeServer {
  private app: Hono;
  private nodes: Map<string, Node> = new Map();
  private executors: Map<string, NodeExecutor> = new Map();
  private triggers: Map<string, TriggerDefinition> = new Map();
  private pages: Map<string, PageDefinition> = new Map();
  private modals: Map<string, ModalDefinition> = new Map();
  private dashboardWidgets: Map<string, DashboardWidgetDefinition> = new Map();
  private drawers: Map<string, DrawerDefinition> = new Map();
  private emailBuilderBlocks: Map<string, EmailBuilderBlockDefinition> =
    new Map();
  private funnelsBuilderBlocks: Map<string, FunnelBuilderBlockDefinition> =
    new Map();
  private apiKey?: string;
  private permissions: string[] = [];
  private options: ServerOptions = {};

  /**
   * Create a new NodeServer instance
   *
   * @param options Server options
   */
  constructor(options: ServerOptions = {}) {
    this.app = new Hono();
    this.options = options;

    // Store API key and permissions
    this.apiKey = options.apiKey;
    this.permissions = options.permissions || [];

    // If API key is provided, set up authentication middleware
    if (this.apiKey) {
      this.setupAuthMiddleware();
    }

    // Use nodes from the registry
    for (const node of componentRegistry.getAllNodes()) {
      this.nodes.set(node.definition.id, node);
    }

    // Use executors from the registry
    const executors = componentRegistry.getAllExecutors();
    for (const executor of executors) {
      // We need to find the node ID for this executor
      // For now, we'll assume the executor ID is the same as the node ID
      for (const node of this.nodes.values()) {
        if (node.executor === executor) {
          this.executors.set(node.definition.id, executor);
          break;
        }
      }
    }

    // Use triggers from the registry
    for (const trigger of componentRegistry.getAllTriggers()) {
      this.triggers.set(trigger.id, trigger);
    }

    // Set up routes using modular approach
    this.setupModularRoutes();
  }

  /**
   * Set up authentication middleware
   *
   * This middleware checks for the API key in the Authorization header
   * and verifies that it matches the one provided to the server.
   * The health endpoint is excluded from authentication.
   */
  private setupAuthMiddleware(): void {
    this.app.use("*", async (c, next) => {
      // Skip authentication for health endpoint
      if (c.req.path === "/health") {
        await next();
        return;
      }

      const authHeader = c.req.header("Authorization");

      // Check if Authorization header is present and has the correct format
      if (!authHeader || !authHeader.startsWith("Bearer ")) {
        return c.json(
          { error: "Missing or invalid Authorization header" },
          401
        );
      }

      // Extract the API key from the Authorization header
      const providedApiKey = authHeader.substring(7); // Remove 'Bearer ' prefix

      // Verify the API key
      if (providedApiKey !== this.apiKey) {
        return c.json({ error: "Invalid API key" }, 401);
      }

      // API key is valid, continue to the next middleware or route handler
      await next();
    });
  }

  /**
   * Set up routes using modular approach
   */
  private setupModularRoutes(): void {
    // Create server context for routes
    const context: ServerContext = {
      nodes: this.nodes,
      executors: this.executors,
      triggers: this.triggers,
      pages: this.pages,
      modals: this.modals,
      dashboardWidgets: this.dashboardWidgets,
      drawers: this.drawers,
      emailBuilderBlocks: this.emailBuilderBlocks,
      funnelsBuilderBlocks: this.funnelsBuilderBlocks,
      apiKey: this.apiKey,
      permissions: this.permissions,
      options: this.options,
    };

    // Create and mount the router
    const router = createRouter(context);
    this.app.route("/", router);
  }

  /**
   * Get the Hono app instance
   *
   * @returns The Hono app instance
   */
  getApp(): Hono {
    return this.app;
  }

  /**
   * Get the middleware function for the server
   *
   * @returns The middleware function
   */
  getMiddleware() {
    return this.app.fetch;
  }
}
