/**
 * @file Email Builder Block definition for ezWorkflow
 *
 * This file provides the interface and function for defining email builder block components
 * that can be used in the ezWorkflow platform.
 */

import { componentRegistry } from './registry';
import { UiComponentDefinition } from './ui';

/**
 * Email Builder Block Component definition interface
 *
 * This interface extends the base UI component definition with
 * email builder block specific properties.
 */
export interface FunnelBuilderBlockDefinition extends UiComponentDefinition {
  /** Type of the email builder block */
  blockType: 'section' | 'column' | 'row' | 'button' | 'divider' | 'spacer' | 'social' | 'custom';

  /** Whether the block is draggable */
  draggable?: boolean;

  /** Whether the block is resizable */
  resizable?: boolean;

  /** Whether the block is deletable */
  deletable?: boolean;

  /** Whether the block is duplicatable */
  duplicatable?: boolean;

  /** Default content for the block */
  defaultContent?: string;

  /** Default styles for the block */
  defaultStyles?: Record<string, string>;

  /** Available settings for the block */
  settings?: Array<{
    name: string;
    type: 'text' | 'number' | 'color' | 'select' | 'toggle' | 'image';
    label: string;
    description?: string;
    defaultValue?: string | number | boolean;
    options?: Array<{ label: string; value: string }>;
  }>;
}

/**
 * Define an email builder block component
 *
 * @param params Email builder block component definition parameters
 * @returns Email builder block component definition
 */
export const defineFunnelBuilderBlock = (params: any): FunnelBuilderBlockDefinition => {
  // Validate required fields
  if (!params.id) {
    throw new Error('Email builder block ID is required');
  }

  if (!params.name) {
    throw new Error('Email builder block name is required');
  }

  // Create a UI component definition with email builder block specific properties
  const uiComponentDefinition: FunnelBuilderBlockDefinition = {
    ...params,
    screenType: 'fullPage', 
  };

  // Register the email builder block in the registry
  componentRegistry.registerFunnelBuilderBlock(uiComponentDefinition);

  // Return the validated email builder block definition
  return params;
}; 