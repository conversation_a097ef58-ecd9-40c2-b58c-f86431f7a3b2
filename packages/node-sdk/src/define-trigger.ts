/**
 * @file Function for defining triggers
 */

import { TriggerDefinition, TriggerType } from './types';
import * as fieldBuilders from './field-builders';
import { componentRegistry } from './registry';

/**
 * Type for trigger definition parameters
 */
interface TriggerDefinitionParams {
  id?: string; // Added ID field
  name: string;
  version: string;
  categories: string[];
  tags: string[];
  type: 'http' | 'socket';
  registarUrl: string;
  expect?: (fields: typeof fieldBuilders) => Record<string, ReturnType<typeof fieldBuilders[keyof typeof fieldBuilders]>>;
  onEnabled: () => void | Promise<void>; // When someone adds this trigger in a workflow
  onDisabled: () => void | Promise<void>; // When someone removes this trigger in a workflow
  onError: (error: Error) => void | Promise<void>; // When an error occurs
}

/**
 * Define a trigger
 *
 * @param params Trigger definition parameters
 * @returns Trigger definition
 */
export const defineTrigger = (params: TriggerDefinitionParams): TriggerDefinition => {
  // Process expected fields
  const expectFields: Record<string, unknown> = {};
  if (params.expect) {
    const expectDefs = params.expect(fieldBuilders);
    for (const [key, builder] of Object.entries(expectDefs)) {
      if (typeof builder.getDefinition === 'function') {
        expectFields[key] = builder.getDefinition();
      } else {
        throw new Error(`Invalid expect field definition for '${key}'`);
      }
    }
  }

  // Map type string to enum
  const triggerType = params.type === 'http' ? TriggerType.HTTP : TriggerType.SOCKET;

  // Generate ID if not provided
  const id = params.id || `trigger-${Math.random().toString(36).substring(2, 15)}`;

  // Create trigger definition
  const triggerDefinition: TriggerDefinition = {
    id,
    name: params.name,
    version: params.version,
    categories: params.categories,
    tags: params.tags,
    type: triggerType,
    registarUrl: params.registarUrl,
    expect: expectFields,
  };

  // Register the trigger in the registry
  componentRegistry.registerTrigger(triggerDefinition);

  return triggerDefinition;
};
