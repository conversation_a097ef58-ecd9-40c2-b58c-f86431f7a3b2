/**
 * @file Main router that combines all route modules
 */

import { Hono } from "hono";
import createHealthRoutes from "./health";
import createNodeRoutes from "./node";
import createTriggerRoutes from "./triggers";
import createPageRoutes from "./pages";

import { ServerContext } from "./middleware";

/**
 * Create and configure the main router with all route modules
 *
 * @param context Server context containing maps, options, and utility functions
 * @returns Configured Hono router
 */
export function createRouter(context: ServerContext): Hono {
  const router = new Hono();

  // Mount route modules
  router.route("/health", createHealthRoutes(context));
  router.route("/nodes", createNodeRoutes(context));
  router.route("/triggers", createTriggerRoutes(context));
  router.route("/pages", createPageRoutes(context));

  // Root endpoint
  router.get("/", (c) => {
    return c.json({
      status: "ok",
      version: context.options.version || "1.0.0",
    });
  });

  return router;
}
