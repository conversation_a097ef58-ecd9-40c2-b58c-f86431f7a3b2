/**
 * @file Page-related routes
 */

import { Hono } from 'hono';
import { ServerContext, requirePermission, withErrorHandling } from './middleware';

/**
 * Create page routes
 * 
 * @param context Server context
 * @returns Hono router with page routes
 */
export default function createPageRoutes(context: ServerContext): Hono {
  const router = new Hono();

  // Get all pages
  router.get('/',
    requirePermission(context, 'read'),
    withErrorHandling((c) => {
      const pagesObj: Record<string, any> = {};

      for (const [id, page] of context.pages.entries()) {
        const { name, version, categories, tags, visibility } = page;

        pagesObj[id] = {
          name,
          version,
          categories,
          tags,
          visibility,
        };
      }

      return c.json({ pages: pagesObj });
    })
  );

  return router;
}
