/**
 * @file UI component routes
 *
 * This file provides routes for all UI components under the unified /ui base route.
 * Includes dashboard widgets, pages, modals, drawers, email builder blocks, and funnel builder blocks.
 */

import { Hono } from "hono";
import {
  ServerContext,
  requirePermission,
  withErrorHandling,
} from "./middleware";

/**
 * Create UI component routes
 *
 * @param context Server context
 * @returns Hono router with UI component routes
 */
export default function createUiRoutes(context: ServerContext): Hono {
  const router = new Hono();

  // Dashboard Widgets Routes
  // Get all dashboard widgets
  router.get(
    "/dashboard-widgets",
    requirePermission(context, "read"),
    withErrorHandling((c) => {
      const widgetsObj: Record<string, any> = {};

      for (const [id, widget] of context.dashboardWidgets.entries()) {
        const { name, version, categories, tags, visibility, widgetSize } =
          widget;

        widgetsObj[id] = {
          name,
          version,
          categories,
          tags,
          visibility,
          widgetSize,
        };
      }

      return c.json({ dashboardWidgets: widgetsObj });
    })
  );

  // Get dashboard widget by ID
  router.get(
    "/dashboard-widgets/:id",
    requirePermission(context, "read"),
    withErrorHandling((c) => {
      const id = c.req.param("id");
      const widget = context.dashboardWidgets.get(id);

      if (!widget) {
        return c.json(
          { error: `Dashboard widget with ID ${id} not found` },
          404
        );
      }

      return c.json({ dashboardWidget: widget });
    })
  );

  // Pages Routes
  // Get all pages
  router.get(
    "/pages",
    requirePermission(context, "read"),
    withErrorHandling((c) => {
      const pagesObj: Record<string, any> = {};

      for (const [id, page] of context.pages.entries()) {
        const { name, version, categories, tags, visibility } = page;

        pagesObj[id] = {
          name,
          version,
          categories,
          tags,
          visibility,
        };
      }

      return c.json({ pages: pagesObj });
    })
  );

  // Get page by ID
  router.get(
    "/pages/:id",
    requirePermission(context, "read"),
    withErrorHandling((c) => {
      const id = c.req.param("id");
      const page = context.pages.get(id);

      if (!page) {
        return c.json({ error: `Page with ID ${id} not found` }, 404);
      }

      return c.json({ page });
    })
  );

  // Modals Routes
  // Get all modals
  router.get(
    "/modals",
    requirePermission(context, "read"),
    withErrorHandling((c) => {
      const modalsObj: Record<string, any> = {};

      for (const [id, modal] of context.modals.entries()) {
        const { name, version, categories, tags, visibility, modalSize } =
          modal;

        modalsObj[id] = {
          name,
          version,
          categories,
          tags,
          visibility,
          modalSize,
        };
      }

      return c.json({ modals: modalsObj });
    })
  );

  // Get modal by ID
  router.get(
    "/modals/:id",
    requirePermission(context, "read"),
    withErrorHandling((c) => {
      const id = c.req.param("id");
      const modal = context.modals.get(id);

      if (!modal) {
        return c.json({ error: `Modal with ID ${id} not found` }, 404);
      }

      return c.json({ modal });
    })
  );

  // Drawers Routes
  // Get all drawers
  router.get(
    "/drawers",
    requirePermission(context, "read"),
    withErrorHandling((c) => {
      const drawersObj: Record<string, any> = {};

      for (const [id, drawer] of context.drawers.entries()) {
        const {
          name,
          version,
          categories,
          tags,
          visibility,
          drawerSize,
          position,
        } = drawer;

        drawersObj[id] = {
          name,
          version,
          categories,
          tags,
          visibility,
          drawerSize,
          position,
        };
      }

      return c.json({ drawers: drawersObj });
    })
  );

  // Get drawer by ID
  router.get(
    "/drawers/:id",
    requirePermission(context, "read"),
    withErrorHandling((c) => {
      const id = c.req.param("id");
      const drawer = context.drawers.get(id);

      if (!drawer) {
        return c.json({ error: `Drawer with ID ${id} not found` }, 404);
      }

      return c.json({ drawer });
    })
  );

  // Email Builder Blocks Routes
  // Get all email builder blocks
  router.get(
    "/email-builder-blocks",
    requirePermission(context, "read"),
    withErrorHandling((c) => {
      const blocksObj: Record<string, any> = {};

      for (const [id, block] of context.emailBuilderBlocks.entries()) {
        const { name, version, categories, tags, visibility, blockType } =
          block;

        blocksObj[id] = {
          name,
          version,
          categories,
          tags,
          visibility,
          blockType,
        };
      }

      return c.json({ emailBuilderBlocks: blocksObj });
    })
  );

  // Get email builder block by ID
  router.get(
    "/email-builder-blocks/:id",
    requirePermission(context, "read"),
    withErrorHandling((c) => {
      const id = c.req.param("id");
      const block = context.emailBuilderBlocks.get(id);

      if (!block) {
        return c.json(
          { error: `Email builder block with ID ${id} not found` },
          404
        );
      }

      return c.json({ emailBuilderBlock: block });
    })
  );

  // Funnel Builder Blocks Routes
  // Get all funnel builder blocks
  router.get(
    "/funnel-builder-blocks",
    requirePermission(context, "read"),
    withErrorHandling((c) => {
      const blocksObj: Record<string, any> = {};

      for (const [id, block] of context.funnelsBuilderBlocks.entries()) {
        const { name, version, categories, tags, visibility, blockType } =
          block;

        blocksObj[id] = {
          name,
          version,
          categories,
          tags,
          visibility,
          blockType,
        };
      }

      return c.json({ funnelBuilderBlocks: blocksObj });
    })
  );

  // Get funnel builder block by ID
  router.get(
    "/funnel-builder-blocks/:id",
    requirePermission(context, "read"),
    withErrorHandling((c) => {
      const id = c.req.param("id");
      const block = context.funnelsBuilderBlocks.get(id);

      if (!block) {
        return c.json(
          { error: `Funnel builder block with ID ${id} not found` },
          404
        );
      }

      return c.json({ funnelBuilderBlock: block });
    })
  );

  return router;
}
