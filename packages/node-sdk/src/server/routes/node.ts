import { Hono } from 'hono';
import { componentRegistry } from '@/registry';
import { Node, NodeExecutor } from '@/types';


const nodeRoutes = new Hono()
const nodes: Map<string, Node> = new Map();
const executors: Map<string, NodeExecutor> = new Map();
const registeredNodes = componentRegistry.getAllNodes();
const registeredExecutors = componentRegistry.getAllExecutors();

for (const node of registeredNodes) {
    nodes.set(node.definition.id, node);
}

for (const executor of registeredExecutors) {
    // We need to find the node ID for this executor
    // For now, we'll assume the executor ID is the same as the node ID
    for (const node of nodes.values()) {
        if (node.executor === executor) {
            executors.set(node.definition.id, executor);
            break;
        }
    }
}

/**
 * Get all nodes
 */
nodeRoutes.get('/', (c) => c.json(nodes))

/**
 * Get a node by ID
 */
nodeRoutes.get('/:id', (c) => {
    const id = c.req.param('id');
    const node = nodes.get(id);
    if (!node) {
        return c.json({ error: 'Node not found' }, 404);
    }
    return c.json(node);
})

/**
 * Get an executor by node ID
 */
nodeRoutes.get('/:id/execute', async (c) => {
    const id = c.req.param('id');
    if (!executors.has(id)) {
        return c.json({ error: 'Executor not found' }, 404);
    }
    const executor = executors.get(id);
    if (!executor) {
        return c.json({ error: 'Executor not found' }, 404);
    }
    const body = await c.req.json();
    const { inputs, config, auth } = body;

    // Create a simple logger
    const logger = {
        info: (message: string) => console.info(`[${id}] ${message}`),
        warn: (message: string) => console.warn(`[${id}] ${message}`),
        error: (message: string) => console.error(`[${id}] ${message}`),
        debug: (message: string) => console.debug(`[${id}] ${message}`),
    };

    // TODO!: Have a consistent return format for all components
    try {
        // Execute the node
        const result = await executor({ inputs, config, auth, logger });
        return c.json(result);
    } catch (error) {
        return c.json({ error: 'Error executing node' }, 500);
    }
})


export default nodeRoutes