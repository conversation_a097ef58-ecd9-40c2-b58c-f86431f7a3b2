/**
 * @file Modal dialog definition for ezWorkflow
 *
 * This file provides the interface and function for defining modal dialog components
 * that can be used in the ezWorkflow platform.
 */

import { componentRegistry } from './registry';
import { UiComponentDefinition } from './ui';

/**
 * Modal Dialog Component definition interface
 *
 * This interface extends the base UI component definition with
 * modal dialog specific properties.
 */
export interface ModalDefinition extends Omit<UiComponentDefinition, 'screenType'> {
  /** Size of the modal ('small', 'medium', or 'large') */
  modalSize: 'small' | 'medium' | 'large';

  /** Whether the modal is closable by clicking outside */
  closeOnClickOutside?: boolean;

  /** Whether the modal has a close button */
  showCloseButton?: boolean;

  /** Whether the modal is draggable */
  draggable?: boolean;

  /** Whether the modal is resizable */
  resizable?: boolean;

  /** Whether the modal should be centered */
  centered?: boolean;
}

/**
 * Define a modal dialog component
 *
 * @param params Modal dialog component definition parameters
 * @returns Modal dialog component definition
 */
export const defineModal = (params: ModalDefinition): ModalDefinition => {
  // Validate required fields
  if (!params.id) {
    throw new Error('Modal dialog ID is required');
  }

  if (!params.name) {
    throw new Error('Modal dialog name is required');
  }
  // Create a UI component definition with modal dialog specific properties
  const uiComponentDefinition: ModalDefinition = {
    ...params,
  };

  // Register the modal dialog in the registry
  componentRegistry.registerModal(uiComponentDefinition);

  // Return the validated modal dialog definition
  return params;
};
