/**
 * @file Drawer definition for ezWorkflow
 *
 * This file provides the interface and function for defining drawer components
 * that can be used in the ezWorkflow platform.
 */

import { componentRegistry } from './registry';
import { UiComponentDefinition } from './ui';

/**
 * Drawer Component definition interface
 *
 * This interface extends the base UI component definition with
 * drawer specific properties.
 */
export interface DrawerDefinition extends UiComponentDefinition {
  /** Size of the drawer ('small', 'medium', or 'large') */
  drawerSize: 'small' | 'medium' | 'large';

  /** Position of the drawer ('left', 'right', 'top', or 'bottom') */
  position?: 'left' | 'right' | 'top' | 'bottom';

  /** Whether the drawer is closable by clicking outside */
  closeOnClickOutside?: boolean;

  /** Whether the drawer has a close button */
  showCloseButton?: boolean;

  /** Whether the drawer is resizable */
  resizable?: boolean;
}

/**
 * Define a drawer component
 *
 * @param params Drawer component definition parameters
 * @returns Drawer component definition
 */
export const defineDrawer = (params: DrawerDefinition): DrawerDefinition => {
  // Validate required fields
  if (!params.id) {
    throw new Error('Drawer ID is required');
  }

  if (!params.name) {
    throw new Error('Drawer name is required');
  }

  if (!params.menuTitle || Object.keys(params.menuTitle).length === 0) {
    throw new Error('Drawer menuTitle is required and must have at least one language');
  }

  if (!params.drawerSize) {
    throw new Error('Drawer size is required');
  }

  // Create a UI component definition with drawer specific properties
  const uiComponentDefinition: DrawerDefinition = {
    ...params,
  };

  // Register the drawer in the registry
  componentRegistry.registerDrawer(uiComponentDefinition);

  // Return the validated drawer definition
  return params;
};
