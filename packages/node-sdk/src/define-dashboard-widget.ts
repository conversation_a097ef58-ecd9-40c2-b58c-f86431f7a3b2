/**
 * @file Dashboard Widget definition for ezWorkflow
 *
 * This file provides the interface and function for defining dashboard widget components
 * that can be used in the ezWorkflow platform.
 */

import { componentRegistry } from './registry';
import { UiComponentDefinition } from './ui';
/**
 * Dashboard Widget Component definition interface
 *
 * This interface extends the base UI component definition with
 * dashboard widget specific properties.
 */
export interface DashboardWidgetDefinition extends UiComponentDefinition {
  /** Size of the dashboard widget ('small', 'medium', 'large', or 'custom') */
  widgetSize: 'small' | 'medium' | 'large' | 'custom';

  /** Custom width (only used when widgetSize is 'custom') */
  customWidth?: string;

  /** Custom height (only used when widgetSize is 'custom') */
  customHeight?: string;

  /** Whether the widget supports fullscreen mode */
  fullscreenSupport?: boolean;
}

/**
 * Define a dashboard widget component
 *
 * @param params Dashboard widget component definition parameters
 * @returns Dashboard widget component definition
 */
export const defineDashboardWidget = (params: DashboardWidgetDefinition): DashboardWidgetDefinition => {
  // Validate required fields
  const options = {
    ...params,
    version: params.version || '1.0.0',
  };
  if (!params.id) {
    throw new Error('Dashboard widget ID is required');
  }

  if (!params.name) {
    throw new Error('Dashboard widget name is required');
  }


  if (!params.widgetSize) {
    options.widgetSize = 'small';
  }

  // If widget size is custom, validate custom dimensions
  if (params.widgetSize === 'custom' && (!params.customWidth || !params.customHeight)) {
    throw new Error('Custom width and height are required when widget size is custom');
  }

  // Create a UI component definition with dashboard widget specific properties
  const uiComponentDefinition: DashboardWidgetDefinition = options;

  // Register the dashboard widget in the registry
  componentRegistry.registerDashboardWidget(uiComponentDefinition);

  // Return the validated dashboard widget definition
  return params;
};
