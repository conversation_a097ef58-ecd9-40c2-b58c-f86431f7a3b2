/**
 * @file Function for defining nodes
 */

import { v4 as uuidv4 } from 'uuid';
import {
  NodeDefinition,
  InputFieldDefinition,
  OutputFieldDefinition,
  ConfigFieldDefinition,
  Node,
  AuthDefinition,
} from './types';
import * as fieldBuilders from './field-builders';
import * as authBuilders from './auth-builders';
import { componentRegistry } from './registry';

/**
 * Type for node definition parameters
 */
interface NodeDefinitionParams {
  id?: string;
  name: string;
  description: string;
  version: string;
  category: string | string[]; // Support both single category and array of categories
  tags?: string[];
  inputs?: (fields: typeof fieldBuilders) => Record<string, ReturnType<typeof fieldBuilders[keyof typeof fieldBuilders]>>;
  outputs?: (fields: typeof fieldBuilders) => Record<string, ReturnType<typeof fieldBuilders[keyof typeof fieldBuilders]>>;
  config?: (fields: typeof fieldBuilders) => Record<string, ReturnType<typeof fieldBuilders[keyof typeof fieldBuilders]>>;
  auth?: Record<string, unknown> | ((builders: any) => Record<string, any>);
  metadata?: Record<string, unknown>;
}

/**
 * Define a node
 *
 * @param params Node definition parameters
 * @returns Node definition
 */
export const defineNode = (params: NodeDefinitionParams): Node => {
  // Generate ID if not provided
  const id = params.id || `trigger-${Math.random().toString(36).substring(2, 15)}`;

  // Process inputs
  const inputFields: Record<string, InputFieldDefinition> = {};
  if (params.inputs) {
    const inputDefs = params.inputs(fieldBuilders);
    for (const [key, builder] of Object.entries(inputDefs)) {
      if (typeof builder.getDefinition === 'function') {
        inputFields[key] = builder.getDefinition() as InputFieldDefinition;
      } else {
        throw new Error(`Invalid input field definition for '${key}'`);
      }
    }
  }

  // Process outputs
  const outputFields: Record<string, OutputFieldDefinition> = {};
  if (params.outputs) {
    const outputDefs = params.outputs(fieldBuilders);
    for (const [key, builder] of Object.entries(outputDefs)) {
      if (typeof builder.getDefinition === 'function') {
        outputFields[key] = builder.getDefinition() as OutputFieldDefinition;
      } else {
        throw new Error(`Invalid output field definition for '${key}'`);
      }
    }
  }

  // Process config
  const configFields: Record<string, ConfigFieldDefinition> = {};
  if (params.config) {
    const configDefs = params.config(fieldBuilders);
    for (const [key, builder] of Object.entries(configDefs)) {
      if (typeof builder.getDefinition === 'function') {
        configFields[key] = builder.getDefinition() as ConfigFieldDefinition;
      } else {
        throw new Error(`Invalid config field definition for '${key}'`);
      }
    }
  }

  // Process auth
  let authFields: Record<string, unknown> = {};
  if (params.auth) {
    if (typeof params.auth === 'function') {
      // Process auth builders
      const authDefs = params.auth(authBuilders);
      for (const [key, builder] of Object.entries(authDefs)) {
        if (typeof builder.getDefinition === 'function') {
          authFields[key] = builder.getDefinition();
        } else {
          throw new Error(`Invalid auth definition for '${key}'`);
        }
      }
    } else {
      authFields = params.auth;
    }
  }

  // Create node definition
  const definition: NodeDefinition = {
    id,
    name: params.name,
    description: params.description,
    version: params.version,
    category: params.category,
    tags: params.tags || [],
    inputs: inputFields,
    outputs: outputFields,
    config: configFields,
    auth: authFields,
    metadata: params.metadata || {},
  };

  // Create node
  const node: Node = {
    definition,
  };

  // Register node in the registry
  componentRegistry.registerNode(node);

  // Return node
  return node;
};
