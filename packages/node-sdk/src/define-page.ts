/**
 * @file Page definition for ezWorkflow
 *
 * This file provides the interface and function for defining page components
 * that can be used in the ezWorkflow platform.
 */

import { componentRegistry } from './registry';
import { UiComponentDefinition } from './ui';

/**
 * Page Component definition interface
 *
 * This interface extends the base UI component definition with
 * page specific properties.
 */
export interface PageDefinition extends UiComponentDefinition {
  /** Route path for the page */
  route?: string;

  /** Whether the page requires authentication */
  requiresAuth?: boolean;

  /** Roles that have access to this page */
  allowedRoles?: string[];

  /** Whether the page should be included in navigation */
  showInNavigation?: boolean;

  /** Navigation group for the page */
  navigationGroup?: string;

  /** Navigation order (lower numbers appear first) */
  navigationOrder?: number;
}

/**
 * Define a page component
 *
 * @param params Page component definition parameters
 * @returns Page component definition
 */
export const definePage = (params: PageDefinition): PageDefinition => {
  // Validate required fields
  if (!params.id) {
    throw new Error('Page ID is required');
  }

  if (!params.name) {
    throw new Error('Page name is required');
  }

  if (!params.url) {
    throw new Error('Page URL is required');
  }

  // Create a UI component definition with page specific properties
  const uiComponentDefinition: PageDefinition = {
    ...params,
  };

  // Register the page in the registry
  componentRegistry.registerPage(uiComponentDefinition);

  // Return the validated page definition
  return params;
};
